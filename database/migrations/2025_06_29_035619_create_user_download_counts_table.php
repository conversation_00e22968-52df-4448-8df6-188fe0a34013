<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_download_counts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->nullable()->constrained()->onDelete('set null');
            $table->date('date');
            $table->integer('daily_count')->default(0);
            $table->integer('total_count')->default(0); // Total downloads for current subscription period
            $table->timestamps();

            // Unique constraint to ensure one record per user per date
            $table->unique(['user_id', 'date']);

            // Index for efficient queries
            $table->index(['user_id', 'subscription_id']);
            $table->index(['date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_download_counts');
    }
};
