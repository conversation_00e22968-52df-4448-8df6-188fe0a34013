<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            $table->integer('download_limit')->nullable()->after('duration_days')->comment('Total downloads allowed per subscription period. NULL means unlimited.');
            $table->integer('daily_download_limit')->nullable()->after('download_limit')->comment('Maximum downloads allowed per day. NULL means unlimited.');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            $table->dropColumn(['download_limit', 'daily_download_limit']);
        });
    }
};
