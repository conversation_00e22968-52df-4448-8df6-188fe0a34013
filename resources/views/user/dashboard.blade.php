@extends('layouts.user')

@section('title', 'Account Dashboard - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Manage your account, profile, businesses, and design projects')

@push('styles')
<style>
    /* Dashboard Header */
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 4rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .dashboard-header .container {
        position: relative;
        z-index: 1;
    }

    .dashboard-header h1 {
        color: white;
        margin-bottom: 0.75rem;
        font-size: 2.75rem;
        font-weight: 800;
        line-height: 1.2;
    }

    .dashboard-header p {
        opacity: 0.9;
        font-size: 1.25rem;
        margin-bottom: 2rem;
        max-width: 600px;
    }

    .welcome-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 2rem;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .quick-action-btn {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.25);
        padding: 1rem 1.75rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quick-action-btn:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .quick-action-btn i {
        font-size: 1.1rem;
    }
    
    /* Stats Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.06);
        border: 1px solid #f1f5f9;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-align: center;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
    }

    .stat-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        border-color: rgba(59, 130, 246, 0.2);
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        margin: 0 auto 1rem;
    }

    .stat-value {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.25rem;
        line-height: 1;
    }

    .stat-label {
        color: #64748b;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .stat-trend {
        margin-top: 0.5rem;
        font-size: 0.75rem;
        color: #059669;
        font-weight: 500;
    }

    .stat-trend.negative {
        color: #dc2626;
    }
    
    /* Dashboard Tabs */
    .dashboard-tabs {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #f1f5f9;
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .tab-nav {
        display: flex;
        background: #f8fafc;
        border-bottom: 1px solid #f1f5f9;
        overflow-x: auto;
    }

    .tab-nav::-webkit-scrollbar {
        height: 3px;
    }

    .tab-nav::-webkit-scrollbar-track {
        background: #f1f5f9;
    }

    .tab-nav::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 2px;
    }

    .tab-button {
        flex: 1;
        min-width: 180px;
        padding: 1.25rem 1.5rem;
        background: none;
        border: none;
        color: #64748b;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-size: 0.95rem;
    }

    .tab-button:hover {
        color: var(--primary-color);
        background: rgba(59, 130, 246, 0.05);
    }

    .tab-button.active {
        color: var(--primary-color);
        background: white;
        font-weight: 700;
    }

    .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    }

    .tab-content {
        padding: 2rem;
    }

    .tab-panel {
        display: none;
    }

    .tab-panel.active {
        display: block;
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Section Headers */
    .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f1f5f9;
    }

    .section-header h3 {
        margin: 0;
        color: #1e293b;
        font-size: 1.25rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-header .icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    /* Profile Form */
    .profile-form {
        max-width: 600px;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.75rem;
        font-weight: 600;
        color: #374151;
        font-size: 0.95rem;
    }

    .form-input {
        width: 100%;
        padding: 14px 18px;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 16px;
        transition: all 0.2s ease;
        background: white;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-input:disabled {
        background-color: #f9fafb;
        cursor: not-allowed;
        color: #6b7280;
    }

    .account-type {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: #f0fdf4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        color: #059669;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .note {
        color: #64748b;
        font-size: 0.875rem;
        margin-top: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Business Profile Styles */
    .business-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .business-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        background: white;
        transition: all 0.3s ease;
    }

    .business-item:hover {
        border-color: var(--primary-color);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
    }

    .business-item.default {
        border-color: #10b981;
        background: #f0fdf4;
    }

    .business-logo {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
        overflow: hidden;
        flex-shrink: 0;
    }

    .business-logo-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .business-info {
        flex: 1;
    }

    .business-name {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .default-badge {
        background: #10b981;
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-weight: 500;
    }

    .business-meta {
        color: #64748b;
        font-size: 0.875rem;
    }

    .business-actions {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
    }

    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(4px);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .modal-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .modal-content {
        background: white;
        border-radius: 16px;
        padding: 0;
        max-width: 600px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        transform: scale(0.9) translateY(20px);
        transition: all 0.3s ease;
    }

    .modal-overlay.active .modal-content {
        transform: scale(1) translateY(0);
    }

    .modal-header {
        padding: 2rem 2rem 1rem 2rem;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .modal-header h3 {
        margin: 0;
        color: #1e293b;
        font-size: 1.5rem;
        font-weight: 700;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #64748b;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .modal-close:hover {
        background: #f1f5f9;
        color: #1e293b;
    }

    .modal-body {
        padding: 2rem;
    }

    .modal-footer {
        padding: 1rem 2rem 2rem 2rem;
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    /* Business Profile Styles */
    .business-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .business-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        background: white;
        transition: all 0.3s ease;
    }

    .business-item:hover {
        border-color: var(--primary-color);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
    }

    .business-item.default {
        border-color: #10b981;
        background: #f0fdf4;
    }

    .business-logo {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
        overflow: hidden;
    }

    .business-logo-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .business-info {
        flex: 1;
    }

    .business-name {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .default-badge {
        background: #10b981;
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-weight: 500;
    }

    .business-meta {
        color: #64748b;
        font-size: 0.875rem;
    }

    .business-actions {
        display: flex;
        gap: 0.5rem;
    }

    /* Recent Activity */
    .activity-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 0;
        border-bottom: 1px solid #f1f5f9;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-weight: 500;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }

    .activity-time {
        color: #64748b;
        font-size: 0.875rem;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .dashboard-content {
            grid-template-columns: 1fr;
        }

        .sidebar-content {
            order: -1;
        }
    }

    @media (max-width: 768px) {
        .welcome-section {
            flex-direction: column;
            text-align: center;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .stat-card {
            padding: 1.5rem;
        }

        .dashboard-header {
            padding: 2rem 0;
        }

        .dashboard-header h1 {
            font-size: 2rem;
        }

        .quick-actions {
            width: 100%;
            justify-content: center;
        }

        .quick-action-btn {
            flex: 1;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .business-item {
            flex-direction: column;
            text-align: center;
        }

        .business-actions {
            width: 100%;
            justify-content: center;
        }
    }
</style>
@endpush

@section('content')
<!-- Dashboard Header -->
<section class="dashboard-header">
    <div class="container">
        <div class="welcome-section">
            <div class="welcome-info">
                <h1>Welcome back, {{ $user->name }}! 👋</h1>
                <p>Ready to create amazing designs? Let's get started with your projects.</p>
            </div>
            <div class="quick-actions">
                <a href="{{ route('user.categories') }}" class="quick-action-btn">
                    <i class="fas fa-palette"></i> Create Design
                </a>
                <a href="{{ route('user.businesses.index') }}" class="quick-action-btn">
                    <i class="fas fa-building"></i> Business Profiles
                </a>
                <a href="{{ route('user.downloads') }}" class="quick-action-btn">
                    <i class="fas fa-download"></i> My Downloads
                </a>
                <a href="{{ route('user.payment.plans') }}" class="quick-action-btn">
                    <i class="fas fa-crown"></i> Upgrade
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Stats Grid -->
<div class="container">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-download"></i>
            </div>
            <div class="stat-value">{{ $stats['total_downloads'] }}</div>
            <div class="stat-label">Total Downloads</div>
            <div class="stat-trend">
                @if($downloadLimits['has_subscription'] && $downloadLimits['total_limit'])
                    <i class="fas fa-chart-pie"></i> {{ $downloadLimits['total_remaining'] }} remaining
                @else
                    <i class="fas fa-arrow-up"></i> +{{ $stats['this_month_downloads'] }} this month
                @endif
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            @if($downloadLimits['has_subscription'] && $downloadLimits['daily_limit'])
                <div class="stat-value">{{ $downloadLimits['daily_remaining'] ?? 0 }}</div>
                <div class="stat-label">Daily Remaining</div>
                <div class="stat-trend">
                    <i class="fas fa-clock"></i> of {{ $downloadLimits['daily_limit'] }} allowed
                </div>
            @else
                <div class="stat-value">{{ $stats['this_month_downloads'] }}</div>
                <div class="stat-label">This Month</div>
                <div class="stat-trend">
                    <i class="fas fa-chart-line"></i> Active period
                </div>
            @endif
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="stat-value">₹{{ number_format($stats['wallet_balance'], 2) }}</div>
            <div class="stat-label">Wallet Balance</div>
            <div class="stat-trend">
                <i class="fas fa-info-circle"></i> Available funds
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-building"></i>
            </div>
            <div class="stat-value">{{ $stats['total_businesses'] }}</div>
            <div class="stat-label">Business Profiles</div>
            <div class="stat-trend">
                <i class="fas fa-check-circle"></i> Ready to use
            </div>
        </div>
    </div>

    <!-- Dashboard Tabs -->
    <div class="dashboard-tabs">
        <div class="tab-nav">
            <button class="tab-button active" onclick="switchTab('profile-settings')">
                <i class="fas fa-user-cog"></i> Profile Settings
            </button>
            <button class="tab-button" onclick="switchTab('business-profiles')">
                <i class="fas fa-building"></i> Business Profiles
            </button>
            <button class="tab-button" onclick="switchTab('subscription')">
                <i class="fas fa-crown"></i> Subscription
            </button>
            <button class="tab-button" onclick="switchTab('transactions')">
                <i class="fas fa-receipt"></i> Recent Transactions
            </button>
            <button class="tab-button" onclick="switchTab('account-actions')">
                <i class="fas fa-cog"></i> Account Actions
            </button>
        </div>

        <div class="tab-content">
            <!-- Profile Settings Tab -->
            <div id="profile-settings" class="tab-panel active">
                <div class="section-header">
                    <h3>
                        <div class="icon">
                            <i class="fas fa-user"></i>
                        </div>
                        Profile Settings
                    </h3>
                </div>
                <div class="profile-form">
                    <div class="form-group">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" id="name" value="{{ $user->name }}" class="form-input" disabled>
                    </div>
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" id="email" value="{{ $user->email }}" class="form-input" disabled>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Account Type</label>
                        <div class="account-type">
                            <i class="fab fa-google"></i> Google Account
                        </div>
                    </div>
                    <p class="note">
                        <i class="fas fa-info-circle"></i>
                        Profile information is managed through your Google account and cannot be changed here.
                    </p>
                </div>
            </div>

            <!-- Business Profiles Tab -->
            <div id="business-profiles" class="tab-panel">
                <div class="section-header">
                    <h3>
                        <div class="icon">
                            <i class="fas fa-building"></i>
                        </div>
                        Business Profiles
                    </h3>
                    <button class="btn btn-primary" onclick="openBusinessModal()">
                        <i class="fas fa-plus"></i> Add Business
                    </button>
                </div>
                @if($businesses->count() > 0)
                    <div class="business-list" id="businessList">
                        @foreach($businesses as $business)
                            <div class="business-item {{ $business->is_default ? 'default' : '' }}" data-business-id="{{ $business->id }}">
                                <div class="business-logo">
                                    @if($business->logo_path)
                                        <img src="{{ Storage::url($business->logo_path) }}" alt="{{ $business->name }}" class="business-logo-img">
                                    @else
                                        <i class="fas fa-building"></i>
                                    @endif
                                </div>
                                <div class="business-info">
                                    <div class="business-name">
                                        {{ $business->name }}
                                        @if($business->is_default)
                                            <span class="default-badge">Default</span>
                                        @endif
                                    </div>
                                    <div class="business-meta">
                                        @if($business->email)
                                            <i class="fas fa-envelope"></i> {{ $business->email }}
                                        @elseif($business->phone)
                                            <i class="fas fa-phone"></i> {{ $business->phone }}
                                        @else
                                            <i class="fas fa-info-circle"></i> No contact info
                                        @endif
                                    </div>
                                </div>
                                <div class="business-actions">
                                    @if(!$business->is_default)
                                        <button class="btn btn-outline btn-sm" onclick="setDefaultBusiness({{ $business->id }})" title="Set as Default">
                                            <i class="fas fa-star"></i>
                                        </button>
                                    @endif
                                    <button class="btn btn-outline btn-sm" onclick="editBusiness({{ $business->id }})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    @if($businesses->count() > 1)
                                        <button class="btn btn-outline btn-sm btn-danger" onclick="deleteBusiness({{ $business->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state" id="emptyState" style="text-align: center; padding: 3rem; color: #64748b;">
                        <i class="fas fa-building" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h4>No Business Profiles</h4>
                        <p>Add your first business profile to get started</p>
                        <button class="btn btn-primary" onclick="openBusinessModal()">
                            <i class="fas fa-plus"></i> Add Business
                        </button>
                    </div>
                @endif
            </div>

            <!-- Subscription Tab -->
            <div id="subscription" class="tab-panel">
                <div class="section-header">
                    <h3>
                        <div class="icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        Subscription Status
                    </h3>
                    <a href="{{ route('user.payment.plans') }}" class="btn btn-primary">
                        <i class="fas fa-crown"></i> View Plans
                    </a>
                </div>
                @if($activeSubscription)
                    <div class="subscription-card active" style="padding: 2rem; background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%); border: 1px solid #bbf7d0; border-radius: 12px;">
                        <div class="subscription-info">
                            <h4 style="color: #059669; margin-bottom: 0.5rem;">{{ $activeSubscription->plan->name }}</h4>
                            <p style="color: #065f46; margin-bottom: 1rem;">Active until {{ $activeSubscription->end_date->format('F j, Y') }}</p>

                            <!-- Download Limits Section -->
                            @if($downloadLimits['has_subscription'])
                                <div class="download-limits" style="margin-bottom: 1rem; padding: 1rem; background: rgba(255, 255, 255, 0.5); border-radius: 8px;">
                                    <h5 style="color: #059669; margin-bottom: 0.75rem; font-size: 0.9rem; font-weight: 600;">Download Usage</h5>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                        @if($downloadLimits['daily_limit'])
                                            <div class="limit-item">
                                                <div style="color: #065f46; font-size: 0.8rem; margin-bottom: 0.25rem;">Daily Downloads</div>
                                                <div style="color: #059669; font-weight: 600;">
                                                    {{ $downloadLimits['daily_remaining'] ?? 0 }} / {{ $downloadLimits['daily_limit'] }} remaining
                                                </div>
                                                <div style="background: #e5e7eb; height: 4px; border-radius: 2px; margin-top: 0.25rem;">
                                                    <div style="background: #059669; height: 100%; border-radius: 2px; width: {{ $downloadLimits['daily_limit'] > 0 ? (($downloadLimits['daily_used'] / $downloadLimits['daily_limit']) * 100) : 0 }}%;"></div>
                                                </div>
                                            </div>
                                        @endif

                                        @if($downloadLimits['total_limit'])
                                            <div class="limit-item">
                                                <div style="color: #065f46; font-size: 0.8rem; margin-bottom: 0.25rem;">Total Downloads</div>
                                                <div style="color: #059669; font-weight: 600;">
                                                    {{ $downloadLimits['total_remaining'] ?? 0 }} / {{ $downloadLimits['total_limit'] }} remaining
                                                </div>
                                                <div style="background: #e5e7eb; height: 4px; border-radius: 2px; margin-top: 0.25rem;">
                                                    <div style="background: #059669; height: 100%; border-radius: 2px; width: {{ $downloadLimits['total_limit'] > 0 ? (($downloadLimits['total_used'] / $downloadLimits['total_limit']) * 100) : 0 }}%;"></div>
                                                </div>
                                            </div>
                                        @endif

                                        @if(!$downloadLimits['daily_limit'] && !$downloadLimits['total_limit'])
                                            <div class="limit-item" style="grid-column: 1 / -1; text-align: center;">
                                                <div style="color: #059669; font-weight: 600;">
                                                    <i class="fas fa-infinity"></i> Unlimited Downloads
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endif

                            <div class="subscription-features" style="display: flex; gap: 1rem; flex-wrap: wrap;">
                                <span class="feature" style="color: #059669;"><i class="fas fa-check"></i> Premium Templates</span>
                                <span class="feature" style="color: #059669;"><i class="fas fa-check"></i> Priority Support</span>
                                <span class="feature" style="color: #059669;"><i class="fas fa-check"></i> High Quality Downloads</span>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="subscription-card inactive" style="padding: 2rem; background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; text-align: center;">
                        <div class="subscription-info">
                            <h4 style="color: #64748b; margin-bottom: 0.5rem;">No Active Subscription</h4>
                            <p style="color: #64748b; margin-bottom: 1rem;">Upgrade to unlock premium features and download limits based on your plan</p>
                            <a href="{{ route('user.payment.plans') }}" class="btn btn-primary">
                                <i class="fas fa-crown"></i> View Plans
                            </a>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Recent Transactions Tab -->
            <div id="transactions" class="tab-panel">
                <div class="section-header">
                    <h3>
                        <div class="icon">
                            <i class="fas fa-receipt"></i>
                        </div>
                        Recent Transactions
                    </h3>
                </div>
                @if($recentTransactions && $recentTransactions->count() > 0)
                    <div class="transactions-list">
                        @foreach($recentTransactions as $transaction)
                            <div class="transaction-item" style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; border: 1px solid #e2e8f0; border-radius: 8px; margin-bottom: 1rem;">
                                <div class="transaction-info">
                                    <h4 style="margin: 0 0 0.25rem 0; color: #1e293b;">{{ $transaction->description ?? 'Payment' }}</h4>
                                    <p style="margin: 0; color: #64748b; font-size: 0.875rem;">{{ $transaction->created_at->format('M j, Y \a\t g:i A') }}</p>
                                </div>
                                <div class="transaction-amount" style="text-align: right;">
                                    <span style="font-weight: 600; color: {{ $transaction->type === 'credit' ? '#059669' : '#dc2626' }};">
                                        {{ $transaction->type === 'credit' ? '+' : '-' }}₹{{ number_format($transaction->amount, 2) }}
                                    </span>
                                    <div style="font-size: 0.75rem; color: #64748b; text-transform: uppercase;">
                                        {{ $transaction->status }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="transactions-list" style="text-align: center; padding: 3rem; color: #64748b;">
                        <i class="fas fa-receipt" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h4>No Transaction History</h4>
                        <p>Your payment and transaction history will appear here once you make purchases.</p>
                        <a href="{{ route('user.payment.plans') }}" class="btn btn-outline">
                            <i class="fas fa-credit-card"></i> Make a Purchase
                        </a>
                    </div>
                @endif
            </div>

            <!-- Account Actions Tab -->
            <div id="account-actions" class="tab-panel">
                <div class="section-header">
                    <h3>
                        <div class="icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        Account Actions
                    </h3>
                </div>
                <div class="account-actions" style="display: flex; flex-direction: column; gap: 1.5rem;">
                    <div class="action-item" style="display: flex; justify-content: space-between; align-items: center; padding: 1.5rem; border: 1px solid #e2e8f0; border-radius: 12px;">
                        <div class="action-info">
                            <h4 style="margin: 0 0 0.5rem 0; color: #1e293b;"><i class="fas fa-download"></i> Download Your Data</h4>
                            <p style="margin: 0; color: #64748b;">Export all your account data including designs and business profiles</p>
                        </div>
                        <button class="btn btn-outline" onclick="downloadAccountData()">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                    </div>

                    <div class="action-item danger" style="display: flex; justify-content: space-between; align-items: center; padding: 1.5rem; border: 1px solid #fecaca; border-radius: 12px; background: #fef2f2;">
                        <div class="action-info">
                            <h4 style="margin: 0 0 0.5rem 0; color: #dc2626;"><i class="fas fa-trash"></i> Delete Account</h4>
                            <p style="margin: 0; color: #991b1b;">Permanently delete your account and all associated data</p>
                        </div>
                        <button class="btn btn-danger" onclick="confirmDeleteAccount()">
                            <i class="fas fa-trash"></i> Delete Account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Business Modal -->
<div id="businessModal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">Add Business Profile</h3>
            <button class="modal-close" onclick="closeBusinessModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="businessForm">
                <div class="form-group">
                    <label for="businessName" class="form-label">Business Name</label>
                    <input type="text" id="businessName" name="name" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="businessEmail" class="form-label">Email Address</label>
                    <input type="email" id="businessEmail" name="email" class="form-input">
                </div>

                <div class="form-group">
                    <label for="businessPhone" class="form-label">Phone Number</label>
                    <input type="tel" id="businessPhone" name="phone" class="form-input">
                </div>

                <div class="form-group">
                    <label for="businessWebsite" class="form-label">Website</label>
                    <input type="url" id="businessWebsite" name="website" class="form-input">
                </div>

                <div class="form-group">
                    <label for="businessAddress" class="form-label">Address</label>
                    <textarea id="businessAddress" name="address" class="form-input" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="businessSlogan" class="form-label">Slogan</label>
                    <input type="text" id="businessSlogan" name="slogan" class="form-input">
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-outline" onclick="closeBusinessModal()">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="saveBusiness()">
                <i class="fas fa-save"></i> Save Business
            </button>
        </div>
    </div>
</div>
</div>


@push('scripts')
<script>
    // Tab switching functionality
    function switchTab(tabId) {
        // Remove active class from all tabs and panels
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

        // Add active class to clicked tab and corresponding panel
        event.target.classList.add('active');
        document.getElementById(tabId).classList.add('active');
    }

    // Business management functions
    function openBusinessModal(businessId = null) {
        const modal = document.getElementById('businessModal');
        const modalTitle = document.getElementById('modalTitle');
        const form = document.getElementById('businessForm');

        // Reset form
        form.reset();

        if (businessId) {
            modalTitle.textContent = 'Edit Business Profile';
            // Here you would load the business data
            // For now, we'll just show the modal
        } else {
            modalTitle.textContent = 'Add Business Profile';
        }

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    function closeBusinessModal() {
        const modal = document.getElementById('businessModal');
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }

    function saveBusiness() {
        const form = document.getElementById('businessForm');
        const formData = new FormData(form);

        // Basic validation
        if (!formData.get('name')) {
            showNotification('Business name is required', 'error');
            return;
        }

        // Here you would make an AJAX call to save the business
        showNotification('Business profile functionality will be implemented soon.', 'info');
        closeBusinessModal();
    }

    function setDefaultBusiness(businessId) {
        if (confirm('Set this business as your default?')) {
            // Here you would make an AJAX call to update the default business
            showNotification('Default business functionality will be implemented soon.', 'info');
        }
    }

    function editBusiness(businessId) {
        openBusinessModal(businessId);
    }

    function deleteBusiness(businessId) {
        if (confirm('Are you sure you want to delete this business profile?')) {
            // Here you would make an AJAX call to delete the business
            showNotification('Delete business functionality will be implemented soon.', 'info');
        }
    }

    function downloadAccountData() {
        if (confirm('This will generate and download a file containing all your account data. Continue?')) {
            // Create a simple data export
            const userData = {
                name: '{{ $user->name }}',
                email: '{{ $user->email }}',
                memberSince: '{{ $user->created_at->format('Y-m-d H:i:s') }}',
                totalBusinesses: {{ $stats['total_businesses'] }},
                totalDownloads: {{ $stats['total_downloads'] }},
                walletBalance: {{ $stats['wallet_balance'] }},
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(userData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'account-data-' + new Date().toISOString().split('T')[0] + '.json';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            showNotification('Account data exported successfully!', 'success');
        }
    }

    function confirmDeleteAccount() {
        if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
            if (confirm('This will permanently delete all your data. Are you absolutely sure?')) {
                showNotification('Account deletion feature will be implemented soon.', 'info');
            }
        }
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        const modal = document.getElementById('businessModal');
        if (e.target === modal) {
            closeBusinessModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeBusinessModal();
        }
    });

    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 10000;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#dc2626' : '#3b82f6'};
            color: white; padding: 1rem 1.5rem; border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
            animation: slideIn 0.3s ease-out;
            max-width: 400px;
        `;
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                ${message}
            </div>
        `;

        document.body.appendChild(notification);
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 5000);
    }

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
</script>
@endpush

@endsection
