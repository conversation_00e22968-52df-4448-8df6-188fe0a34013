@extends('layouts.admin')

@section('title', 'Plans Management')
@section('page-title', 'Plans Management')

@push('styles')
<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #e2e8f0;
    }

    .page-header h1 {
        font-size: 1.875rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0;
    }

    .table-container {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e2e8f0;
        overflow: hidden;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        text-align: left;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    td {
        padding: 1rem;
        border-bottom: 1px solid #e2e8f0;
        color: #334155;
    }

    tr:hover {
        background: #f8fafc;
    }

    tr:last-child td {
        border-bottom: none;
    }

    .btn {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-block;
        text-align: center;
        border: none;
        margin-right: 0.5rem;
    }

    .btn-create {
        background: #10b981;
        color: white;
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .btn-create:hover {
        background: #059669;
        transform: translateY(-1px);
    }

    .btn-edit {
        background: #f59e0b;
        color: white;
    }

    .btn-edit:hover {
        background: #d97706;
    }

    .btn-delete {
        background: #ef4444;
        color: white;
    }

    .btn-delete:hover {
        background: #dc2626;
    }

    .alert {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
        font-weight: 500;
    }

    .alert-success {
        background: #d1fae5;
        color: #065f46;
        border: 1px solid #a7f3d0;
    }

    .alert-error {
        background: #fee2e2;
        color: #991b1b;
        border: 1px solid #fca5a5;
    }

    .status-active {
        color: #059669;
        font-weight: 600;
        background: #d1fae5;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-inactive {
        color: #dc2626;
        font-weight: 600;
        background: #fee2e2;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #64748b;
    }

    .empty-state h3 {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
        color: #334155;
    }
</style>
@endpush

@section('content')
<div class="page-header">
    <h1>📋 Manage Plans</h1>
    <a href="{{ route('admin.plans.create') }}" class="btn btn-create">+ Create New Plan</a>
</div>

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif
@if(session('error'))
    <div class="alert alert-error">
        {{ session('error') }}
    </div>
@endif

<div class="table-container">
    <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Price (INR)</th>
                    <th>Duration (Days)</th>
                    <th>Download Limit</th>
                    <th>Daily Limit</th>
                    <th>Razorpay Plan ID</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
            @forelse ($plans as $plan)
                <tr>
                    <td><strong>#{{ $plan->id }}</strong></td>
                    <td>{{ $plan->name }}</td>
                    <td><strong>₹{{ number_format($plan->price, 2) }}</strong></td>
                    <td>{{ $plan->duration_days }} days</td>
                    <td>{{ $plan->download_limit ?? 'Unlimited' }}</td>
                    <td>{{ $plan->daily_download_limit ?? 'Unlimited' }}</td>
                    <td>{{ $plan->razorpay_plan_id ?? 'N/A' }}</td>
                    <td>
                        @if($plan->status)
                            <span class="status-active">Active</span>
                        @else
                            <span class="status-inactive">Inactive</span>
                        @endif
                    </td>
                    <td>
                        <a href="{{ route('admin.plans.edit', $plan->id) }}" class="btn btn-edit">Edit</a>
                        <form action="{{ route('admin.plans.destroy', $plan->id) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-delete" onclick="return confirm('Are you sure you want to deactivate this plan?');">
                                {{ $plan->status ? 'Deactivate' : 'Activate' }}
                            </button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7">
                        <div class="empty-state">
                            <h3>No plans found</h3>
                            <p>Get started by creating your first subscription plan.</p>
                            <a href="{{ route('admin.plans.create') }}" class="btn btn-create">Create First Plan</a>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>
@endsection
