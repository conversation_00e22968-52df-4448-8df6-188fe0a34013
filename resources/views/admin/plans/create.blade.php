@extends('layouts.admin')

@section('title', 'Create Plan')
@section('page-title', 'Create Plan')

@push('styles')
@include('admin.shared-styles')
@endpush

@section('content')
<div class="page-header">
    <h1>📋 Create New Plan</h1>
    <a href="{{ route('admin.plans.index') }}" class="btn btn-back">← Back to Plans</a>
</div>

@if(session('error'))
    <div class="alert alert-error">
        {{ session('error') }}
    </div>
@endif

@if ($errors->any())
    <div class="alert alert-error">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

<div class="form-container">
    <form action="{{ route('admin.plans.store') }}" method="POST">
        @csrf

        <div class="form-group">
            <label for="name">Plan Name</label>
            <input type="text" name="name" id="name" value="{{ old('name') }}" required>
        </div>

        <div class="form-group">
            <label for="description">Description</label>
            <textarea name="description" id="description" required>{{ old('description') }}</textarea>
        </div>

        <div class="form-group">
            <label for="price">Price (INR)</label>
            <input type="number" name="price" id="price" value="{{ old('price') }}" step="0.01" min="0" required>
        </div>

        <div class="form-group">
            <label for="duration_days">Duration (Days)</label>
            <input type="number" name="duration_days" id="duration_days" value="{{ old('duration_days') }}" min="1" required>
            <small style="color: #6b7280; font-size: 0.875rem;">Example: 1 for daily, 7 for weekly, 30 for monthly, 365 for yearly.</small>
        </div>

        <div class="form-group">
            <label for="download_limit">Download Limit</label>
            <input type="number" name="download_limit" id="download_limit" value="{{ old('download_limit') }}" min="1">
            <small style="color: #6b7280; font-size: 0.875rem;">Total downloads allowed per subscription period. Leave empty for unlimited downloads.</small>
        </div>

        <div class="form-group">
            <label for="daily_download_limit">Daily Download Limit</label>
            <input type="number" name="daily_download_limit" id="daily_download_limit" value="{{ old('daily_download_limit') }}" min="1">
            <small style="color: #6b7280; font-size: 0.875rem;">Maximum downloads allowed per day. Leave empty for unlimited daily downloads.</small>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-create">Create Plan</button>
            <a href="{{ route('admin.plans.index') }}" class="btn btn-back">Cancel</a>
        </div>
    </form>
</div>
@endsection
