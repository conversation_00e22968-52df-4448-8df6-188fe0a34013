<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserDownloadCount extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'subscription_id',
        'date',
        'daily_count',
        'total_count',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
    ];

    /**
     * Get the user that owns the download count.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription associated with the download count.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Increment the daily count for a user on a specific date.
     */
    public static function incrementDailyCount($userId, $subscriptionId = null, $date = null)
    {
        $date = $date ?: now()->toDateString();

        return static::updateOrCreate(
            [
                'user_id' => $userId,
                'date' => $date,
            ],
            [
                'subscription_id' => $subscriptionId,
                'daily_count' => 0,
                'total_count' => 0,
            ]
        )->increment('daily_count');
    }

    /**
     * Increment the total count for a user's current subscription.
     */
    public static function incrementTotalCount($userId, $subscriptionId = null, $date = null)
    {
        $date = $date ?: now()->toDateString();

        return static::updateOrCreate(
            [
                'user_id' => $userId,
                'date' => $date,
            ],
            [
                'subscription_id' => $subscriptionId,
                'daily_count' => 0,
                'total_count' => 0,
            ]
        )->increment('total_count');
    }

    /**
     * Get daily count for a user on a specific date.
     */
    public static function getDailyCount($userId, $date = null)
    {
        $date = $date ?: now()->toDateString();

        $record = static::where('user_id', $userId)
            ->where('date', $date)
            ->first();

        return $record ? $record->daily_count : 0;
    }

    /**
     * Get total count for a user's current subscription.
     */
    public static function getTotalCount($userId, $subscriptionId = null)
    {
        $query = static::where('user_id', $userId);

        if ($subscriptionId) {
            $query->where('subscription_id', $subscriptionId);
        }

        return $query->sum('total_count');
    }

    /**
     * Reset counts for a new subscription period.
     */
    public static function resetForNewSubscription($userId, $newSubscriptionId)
    {
        // Update all existing records to the new subscription
        static::where('user_id', $userId)
            ->update([
                'subscription_id' => $newSubscriptionId,
                'total_count' => 0,
            ]);
    }
}
