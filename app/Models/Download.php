<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Download extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'business_id',
        'template_type',
        'template_id',
        'design_data',
        'file_path',
        'file_name',
        'file_format',
        'file_size',
        'download_type',
        'cost',
        'status',
        'completed_at',
        'error_message',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'design_data' => 'array',
        'cost' => 'decimal:2',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the download.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the business associated with the download.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the template (poster or frame) associated with the download.
     */
    public function template()
    {
        if ($this->template_type === 'poster') {
            return $this->belongsTo(Poster::class, 'template_id');
        } elseif ($this->template_type === 'frame') {
            return $this->belongsTo(Frame::class, 'template_id');
        }
        
        return null;
    }

    /**
     * Get the file URL attribute.
     */
    public function getFileUrlAttribute(): ?string
    {
        if ($this->file_path) {
            return Storage::url($this->file_path);
        }
        return null;
    }

    /**
     * Get the formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Scope to get downloads for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get completed downloads.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get downloads by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get downloads by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('download_type', $type);
    }

    /**
     * Mark download as completed.
     */
    public function markAsCompleted($filePath, $fileSize = null)
    {
        $this->update([
            'status' => 'completed',
            'file_path' => $filePath,
            'file_size' => $fileSize,
            'completed_at' => now(),
            'error_message' => null,
        ]);

        // Record the download count for subscription users
        if ($this->download_type === 'subscription') {
            $this->user->recordDownload();
        }
    }

    /**
     * Mark download as failed.
     */
    public function markAsFailed($errorMessage)
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Delete file when download record is deleted
        static::deleting(function ($download) {
            if ($download->file_path) {
                Storage::delete($download->file_path);
            }
        });
    }
}
