<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'google_id',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
        ];
    }

    /**
     * Get the subscriptions for the user.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the wallet associated with the user.
     */
    public function wallet(): HasOne
    {
        return $this->hasOne(Wallet::class);
    }

    /**
     * Get the transactions for the user.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get the businesses for the user.
     */
    public function businesses(): HasMany
    {
        return $this->hasMany(Business::class);
    }

    /**
     * Get the default business for the user.
     */
    public function defaultBusiness(): HasOne
    {
        return $this->hasOne(Business::class)->where('is_default', true);
    }

    /**
     * Get the downloads for the user.
     */
    public function downloads(): HasMany
    {
        return $this->hasMany(Download::class);
    }

    /**
     * Get the download counts for the user.
     */
    public function downloadCounts(): HasMany
    {
        return $this->hasMany(UserDownloadCount::class);
    }

    /**
     * Get the user's active subscription.
     */
    public function activeSubscription()
    {
        return $this->subscriptions()
            ->where('status', 'active')
            ->where('end_date', '>', now())
            ->first();
    }

    /**
     * Check if user can download based on their subscription limits.
     */
    public function canDownload(): array
    {
        $activeSubscription = $this->activeSubscription();

        // If no active subscription, check if free downloads are enabled
        if (!$activeSubscription) {
            $freeDownloadsEnabled = \App\Models\Setting::get('free_download_enabled', 'true') === 'true';
            return [
                'can_download' => $freeDownloadsEnabled,
                'reason' => $freeDownloadsEnabled ? null : 'No active subscription and free downloads are disabled',
                'type' => $freeDownloadsEnabled ? 'free' : 'paid'
            ];
        }

        $plan = $activeSubscription->plan;
        $today = now()->toDateString();

        // Check daily limit
        if ($plan->daily_download_limit) {
            $dailyCount = UserDownloadCount::getDailyCount($this->id, $today);
            if ($dailyCount >= $plan->daily_download_limit) {
                return [
                    'can_download' => false,
                    'reason' => "Daily download limit reached ({$plan->daily_download_limit} downloads per day)",
                    'type' => 'subscription',
                    'daily_count' => $dailyCount,
                    'daily_limit' => $plan->daily_download_limit
                ];
            }
        }

        // Check total subscription limit
        if ($plan->download_limit) {
            $totalCount = UserDownloadCount::getTotalCount($this->id, $activeSubscription->id);
            if ($totalCount >= $plan->download_limit) {
                return [
                    'can_download' => false,
                    'reason' => "Subscription download limit reached ({$plan->download_limit} downloads per subscription)",
                    'type' => 'subscription',
                    'total_count' => $totalCount,
                    'total_limit' => $plan->download_limit
                ];
            }
        }

        return [
            'can_download' => true,
            'reason' => null,
            'type' => 'subscription'
        ];
    }

    /**
     * Get remaining download counts for the user.
     */
    public function getRemainingDownloads(): array
    {
        $activeSubscription = $this->activeSubscription();

        if (!$activeSubscription) {
            return [
                'daily_remaining' => null,
                'total_remaining' => null,
                'daily_limit' => null,
                'total_limit' => null,
                'has_subscription' => false
            ];
        }

        $plan = $activeSubscription->plan;
        $today = now()->toDateString();

        $dailyCount = UserDownloadCount::getDailyCount($this->id, $today);
        $totalCount = UserDownloadCount::getTotalCount($this->id, $activeSubscription->id);

        return [
            'daily_remaining' => $plan->daily_download_limit ? max(0, $plan->daily_download_limit - $dailyCount) : null,
            'total_remaining' => $plan->download_limit ? max(0, $plan->download_limit - $totalCount) : null,
            'daily_limit' => $plan->daily_download_limit,
            'total_limit' => $plan->download_limit,
            'daily_used' => $dailyCount,
            'total_used' => $totalCount,
            'has_subscription' => true
        ];
    }

    /**
     * Record a download for the user.
     */
    public function recordDownload()
    {
        $activeSubscription = $this->activeSubscription();
        $subscriptionId = $activeSubscription ? $activeSubscription->id : null;
        $today = now()->toDateString();

        // Increment both daily and total counts
        UserDownloadCount::incrementDailyCount($this->id, $subscriptionId, $today);
        UserDownloadCount::incrementTotalCount($this->id, $subscriptionId, $today);
    }
}
