<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthController extends Controller
{
    /**
     * Show the login form - redirect to Google OAuth.
     */
    public function showLogin()
    {
        if (Auth::check()) {
            return redirect()->route('user.home');
        }

        // Since we're using Google-only authentication, redirect to Google OAuth
        return redirect()->route('auth.google');
    }

    /**
     * <PERSON>le login request - redirect to Google OAuth.
     */
    public function login(Request $request)
    {
        // Since we're using Google-only authentication, redirect to Google OAuth
        return redirect()->route('auth.google');
    }



    /**
     * Handle logout request.
     */
    public function logout(Request $request)
    {
        Auth::logout();
        
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('user.login')
            ->with('success', 'You have been logged out successfully.');
    }

    /**
     * Show forgot password form.
     */
    public function showForgotPassword()
    {
        return view('user.auth.forgot-password');
    }

    /**
     * Handle forgot password request.
     */
    public function forgotPassword(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        // For now, just return a success message
        // In a real application, you would send a password reset email
        return back()->with('success', 'If an account with that email exists, we have sent a password reset link.');
    }
}
